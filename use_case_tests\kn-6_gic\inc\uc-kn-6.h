/*
 * A9MPCore GIC中断控制器模块测试用例实现接口
 *
 * 测试用例范围：UC-KN-6-1 ~ UC-KN-6-24
 */

#ifndef UC_KN_6_H
#define UC_KN_6_H

void uc_kn_6_1(void);
void uc_kn_6_2(void);
void uc_kn_6_3(void);
void uc_kn_6_4(void);
void uc_kn_6_5(void);
void uc_kn_6_6(void);
void uc_kn_6_7(void);
void uc_kn_6_8(void);
void uc_kn_6_9(void);
void uc_kn_6_10(void);
void uc_kn_6_11(void);
void uc_kn_6_12(void);
void uc_kn_6_13(void);
void uc_kn_6_14(void);
void uc_kn_6_15(void);
void uc_kn_6_16(void);
void uc_kn_6_17(void);
void uc_kn_6_18(void);
void uc_kn_6_19(void);
void uc_kn_6_20(void);
void uc_kn_6_21(void);
void uc_kn_6_22(void);
void uc_kn_6_23(void);
void uc_kn_6_24(void);

#endif /* UC_KN_6_H */
