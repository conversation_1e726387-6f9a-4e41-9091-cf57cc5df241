/*
 * A9MPCore 全局定时器驱动头文件
 */

#ifndef CORE_TIMER_H
#define CORE_TIMER_H

/* 寄存器定义 */
#define PERIPHBASE                      0x3FFF0000

#define GLOBAL_TIMER_BASE               (PERIPHBASE + 0x200)
#define GLOBAL_TIMER_COUNTER_LOW        (GLOBAL_TIMER_BASE + 0x00)
#define GLOBAL_TIMER_COUNTER_HIGH       (GLOBAL_TIMER_BASE + 0x04)
#define GLOBAL_TIMER_CTLR               (GLOBAL_TIMER_BASE + 0x08)
#define GLOBAL_TIMER_ISR                (GLOBAL_TIMER_BASE + 0x0C)
#define GLOBAL_TIMER_CMP_LOW            (GLOBAL_TIMER_BASE + 0x10)
#define GLOBAL_TIMER_CMP_HIGH           (GLOBAL_TIMER_BASE + 0x14)
#define GLOBAL_TIMER_AUTO_INCR          (GLOBAL_TIMER_BASE + 0x18)

#define PRIVATE_TIMER_BASE              (PERIPHBASE + 0x600)
#define PRIVATE_TIMER_LOAD              (PRIVATE_TIMER_BASE + 0x00)
#define PRIVATE_TIMER_COUNTER           (PRIVATE_TIMER_BASE + 0x04)
#define PRIVATE_TIMER_CTLR              (PRIVATE_TIMER_BASE + 0x08)

#endif /* CORE_TIMER_H */
