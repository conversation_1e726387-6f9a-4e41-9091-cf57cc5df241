/*
 * A9MPCore 私有定时器模块测试用例实现
 *
 * 测试用例范围：UC-KN-4-1 ~ UC-KN-4-5
 */

#include "uc-kn-4.h"

#include "types.h"
#include "bsp_print.h"  /* 串口输出接口 */

/* A9MPCore 私有定时器寄存器定义 */
#define PERIPHBASE                      0x3FFF0000
#define PRIVATE_TIMER_BASE              (PERIPHBASE + 0x600)
#define PRIVATE_TIMER_LOAD              (PRIVATE_TIMER_BASE + 0x00)
#define PRIVATE_TIMER_COUNTER           (PRIVATE_TIMER_BASE + 0x04)
#define PRIVATE_TIMER_CTLR              (PRIVATE_TIMER_BASE + 0x08)
#define PRIVATE_TIMER_ISR               (PRIVATE_TIMER_BASE + 0x0C)

/* 私有定时器寄存器复位值 */
#define PRIVATE_TIMER_LOAD_RESET        0x00000000U
#define PRIVATE_TIMER_COUNTER_RESET     0x00000000U
#define PRIVATE_TIMER_CTLR_RESET        0x00000000U
#define PRIVATE_TIMER_ISR_RESET         0x00000000U

/* 测试用值 */
#define TEST_LOAD_VAL                   0x00100000U
#define TEST_CTLR_VAL                   0x00000007U

/* 控制寄存器位定义 */
#define PTIMER_ENABLE_BIT               (1 << 0)
#define PTIMER_AUTO_RELOAD_BIT          (1 << 1)
#define PTIMER_IRQ_ENABLE_BIT           (1 << 2)

/* 中断状态寄存器位定义 */
#define PTIMER_EVENT_FLAG_BIT           (1 << 0)

/* 简单延时函数 */
static void simple_delay(uint32_t count) {
    volatile uint32_t i;
    for (i = 0; i < count; i++) {
        /* 空循环 */
    }
}

void uc_kn_4_1(void)
{
    print2("[INFO] uc_kn_4_1: Starting private timer registers test\n");

    uint32_t val;

    /* 验证可读寄存器复位值 */
    val = READ_32(PRIVATE_TIMER_LOAD);
    if (val != PRIVATE_TIMER_LOAD_RESET) {
        print2("[FAILED] LOAD reset mismatch: read=0x%x, exp=0x%x\n",
               val, PRIVATE_TIMER_LOAD_RESET);
        return;
    }

    val = READ_32(PRIVATE_TIMER_COUNTER);
    if (val != PRIVATE_TIMER_COUNTER_RESET) {
        print2("[FAILED] COUNTER reset mismatch: read=0x%x, exp=0x%x\n",
               val, PRIVATE_TIMER_COUNTER_RESET);
        return;
    }

    val = READ_32(PRIVATE_TIMER_CTLR);
    if (val != PRIVATE_TIMER_CTLR_RESET) {
        print2("[FAILED] CTLR reset mismatch: read=0x%x, exp=0x%x\n",
               val, PRIVATE_TIMER_CTLR_RESET);
        return;
    }

    val = READ_32(PRIVATE_TIMER_ISR);
    if (val != PRIVATE_TIMER_ISR_RESET) {
        print2("[FAILED] ISR reset mismatch: read=0x%x, exp=0x%x\n",
               val, PRIVATE_TIMER_ISR_RESET);
        return;
    }

    /* 向所有寄存器写入合法值 */
    WRITE_32(PRIVATE_TIMER_LOAD, TEST_LOAD_VAL);
    WRITE_32(PRIVATE_TIMER_CTLR, TEST_CTLR_VAL);
    WRITE_32(PRIVATE_TIMER_ISR, 0x1);  /* 清除中断标志 */

    /* 写入后再次读取，确认可写可读寄存器值为刚才写入值 */
    val = READ_32(PRIVATE_TIMER_LOAD);
    if (val != TEST_LOAD_VAL) {
        print2("[FAILED] LOAD write/read mismatch: read=0x%x, exp=0x%x\n",
               val, TEST_LOAD_VAL);
        return;
    }

    val = READ_32(PRIVATE_TIMER_CTLR);
    if (val != TEST_CTLR_VAL) {
        print2("[FAILED] CTLR write/read mismatch: read=0x%x, exp=0x%x\n",
               val, TEST_CTLR_VAL);
        return;
    }

    /* 验证不可读寄存器是否通过QEMU日志反馈读取无效 */
    print2("[INFO] Checking QEMU log for invalid-read entries on write-only registers\n");
    print2("[PASSED] uc_kn_4_1: Private timer registers test completed successfully\n");
}

void uc_kn_4_2(void)
{
    print2("[INFO] uc_kn_4_2: Starting private timer load register test\n");

    uint32_t load_val = 0x00080000;  /* 加载值 */
    uint32_t counter_val, prev_counter;
    uint32_t timeout_count = 0;
    const uint32_t max_timeout = 10000;

    /* 清除私有定时器控制寄存器的定时器使能位 */
    WRITE_32(PRIVATE_TIMER_CTLR, 0x00000000);

    /* 设置私有定时器的加载寄存器的值为load_val */
    WRITE_32(PRIVATE_TIMER_LOAD, load_val);

    /* 设置定时器使能位 */
    WRITE_32(PRIVATE_TIMER_CTLR, PTIMER_ENABLE_BIT);

    /* 不断读取私有定时器的计数器值，观察到其不断减少直到定时器值为0 */
    print2("[INFO] Monitoring counter decreasing from load_val=0x%x...\n", load_val);

    prev_counter = READ_32(PRIVATE_TIMER_COUNTER);
    
    while (timeout_count < max_timeout) {
        counter_val = READ_32(PRIVATE_TIMER_COUNTER);
        
        if (counter_val > prev_counter) {
            print2("[FAILED] Counter should decrease: prev=0x%x, curr=0x%x\n",
                   prev_counter, counter_val);
            return;
        }
        
        if (counter_val == 0) {
            print2("[INFO] Counter reached 0 as expected\n");
            break;
        }
        
        prev_counter = counter_val;
        timeout_count++;
        simple_delay(100);
    }

    if (timeout_count >= max_timeout) {
        print2("[FAILED] Timeout waiting for counter to reach 0\n");
        return;
    }

    /* 停止定时器 */
    WRITE_32(PRIVATE_TIMER_CTLR, 0x00000000);

    print2("[PASSED] uc_kn_4_2: Private timer load register test completed successfully\n");
}

void uc_kn_4_3(void)
{
    print2("[INFO] uc_kn_4_3: Starting private timer counter register test\n");

    uint32_t load_val = 0x00040000;  /* 加载值 */
    uint32_t counter_val, prev_counter;
    uint32_t timeout_count = 0;
    const uint32_t max_timeout = 15000;
    uint32_t reload_count = 0;

    /* 清除私有定时器控制寄存器的定时器使能位 */
    WRITE_32(PRIVATE_TIMER_CTLR, 0x00000000);

    /* 设置私有定时器的加载寄存器的值为load_val */
    WRITE_32(PRIVATE_TIMER_LOAD, load_val);

    /* 设置定时器使能位、自动加载使能位 */
    WRITE_32(PRIVATE_TIMER_CTLR, PTIMER_ENABLE_BIT | PTIMER_AUTO_RELOAD_BIT);

    /* 不断读取私有定时器的计数器值，观察到其不断减少且当计数值为0时会自动加载load_val自动重新计数 */
    print2("[INFO] Monitoring counter with auto-reload (load_val=0x%x)...\n", load_val);

    prev_counter = READ_32(PRIVATE_TIMER_COUNTER);
    
    while (timeout_count < max_timeout && reload_count < 2) {
        counter_val = READ_32(PRIVATE_TIMER_COUNTER);
        
        /* 检测自动重载事件 */
        if (counter_val > prev_counter && prev_counter < (load_val / 10)) {
            reload_count++;
            print2("[INFO] Auto-reload detected #%d: prev=0x%x, curr=0x%x\n",
                   reload_count, prev_counter, counter_val);
        }
        
        /* 验证计数器在正常递减 */
        if (counter_val > prev_counter && prev_counter >= (load_val / 10)) {
            print2("[FAILED] Counter should decrease: prev=0x%x, curr=0x%x\n",
                   prev_counter, counter_val);
            return;
        }
        
        prev_counter = counter_val;
        timeout_count++;
        simple_delay(50);
    }

    if (reload_count < 2) {
        print2("[FAILED] Auto-reload not observed (reload_count=%d)\n", reload_count);
        return;
    }

    /* 停止定时器 */
    WRITE_32(PRIVATE_TIMER_CTLR, 0x00000000);

    print2("[PASSED] uc_kn_4_3: Private timer counter register test completed successfully\n");
}

void uc_kn_4_4(void)
{
    print2("[INFO] uc_kn_4_4: Starting private timer interrupt status register test\n");

    uint32_t load_val = 0x00020000;  /* 加载值 */
    uint32_t counter_val;
    uint32_t event_flag;
    uint32_t timeout_count = 0;
    const uint32_t max_timeout = 12000;

    /* 清除私有定时器控制寄存器的定时器使能位 */
    WRITE_32(PRIVATE_TIMER_CTLR, 0x00000000);

    /* 设置私有定时器的加载寄存器的值为load_val */
    WRITE_32(PRIVATE_TIMER_LOAD, load_val);

    /* 清除中断状态 */
    WRITE_32(PRIVATE_TIMER_ISR, 0x1);

    /* 设置定时器使能位 */
    WRITE_32(PRIVATE_TIMER_CTLR, PTIMER_ENABLE_BIT);

    /* 不断读取私有定时器的计数器值，观察到其不断减少且当计数值为0时，中断状态寄存器的事件标志位为1 */
    print2("[INFO] Monitoring counter and event flag (load_val=0x%x)...\n", load_val);

    while (timeout_count < max_timeout) {
        counter_val = READ_32(PRIVATE_TIMER_COUNTER);
        event_flag = READ_32(PRIVATE_TIMER_ISR) & PTIMER_EVENT_FLAG_BIT;

        if (counter_val > 0) {
            /* 当计数器值大于0时，确认事件标志位为0 */
            if (event_flag != 0) {
                print2("[FAILED] Event flag should be 0 when counter(0x%x) > 0: flag=0x%x\n",
                       counter_val, event_flag);
                return;
            }
        } else {
            /* 当计数器值为0时，确认事件标志位为1 */
            if (event_flag == 0) {
                print2("[FAILED] Event flag should be 1 when counter=0: flag=0x%x\n",
                       event_flag);
                return;
            }

            print2("[INFO] Event flag correctly set: counter=0x%x, flag=0x%x\n",
                   counter_val, event_flag);
            break;
        }

        timeout_count++;
        simple_delay(50);
    }

    if (timeout_count >= max_timeout) {
        print2("[FAILED] Timeout waiting for counter to reach 0\n");
        return;
    }

    /* 停止定时器 */
    WRITE_32(PRIVATE_TIMER_CTLR, 0x00000000);

    print2("[PASSED] uc_kn_4_4: Private timer interrupt status register test completed successfully\n");
}

void uc_kn_4_5(void)
{
    print2("[INFO] uc_kn_4_5: Starting private timer interrupt test\n");

    uint32_t load_val = 0x00030000;  /* 加载值 */
    uint32_t counter_val;
    uint32_t event_flag;
    uint32_t timeout_count = 0;
    const uint32_t max_timeout = 15000;

    /* 清除私有定时器控制寄存器的定时器使能位 */
    WRITE_32(PRIVATE_TIMER_CTLR, 0x00000000);

    /* 设置私有定时器的加载寄存器的值为load_val */
    WRITE_32(PRIVATE_TIMER_LOAD, load_val);

    /* 清除中断状态 */
    WRITE_32(PRIVATE_TIMER_ISR, 0x1);

    /* 设置定时器使能位、中断使能位 */
    WRITE_32(PRIVATE_TIMER_CTLR, PTIMER_ENABLE_BIT | PTIMER_IRQ_ENABLE_BIT);

    print2("[INFO] Private timer started with interrupt enabled (load_val=0x%x)\n", load_val);
    print2("[INFO] Note: Check interrupt system for IRQ 29 (Private Timer) when event occurs\n");

    /* 不断读取私有定时器的计数器值和中断状态寄存器的事件标志位 */
    while (timeout_count < max_timeout) {
        counter_val = READ_32(PRIVATE_TIMER_COUNTER);
        event_flag = READ_32(PRIVATE_TIMER_ISR) & PTIMER_EVENT_FLAG_BIT;

        if (counter_val > 0) {
            /* 当计数器值大于0时，确认事件标志位为0 */
            if (event_flag != 0) {
                print2("[FAILED] Event flag should be 0 when counter(0x%x) > 0: flag=0x%x\n",
                       counter_val, event_flag);
                return;
            }
        } else {
            /* 当计数器值为0时，确认事件标志位为1且中断系统触发相应中断 */
            if (event_flag == 0) {
                print2("[FAILED] Event flag should be 1 when counter=0: flag=0x%x\n",
                       event_flag);
                return;
            }

            print2("[INFO] Interrupt event triggered: counter=0x%x, flag=0x%x\n",
                   counter_val, event_flag);
            print2("[INFO] Private Timer IRQ 29 should be triggered in interrupt system\n");

            /* 执行到此处说明中断返回 */
            print2("[INFO] Interrupt returned\n");
            break;
        }

        timeout_count++;
        simple_delay(50);
    }

    if (timeout_count >= max_timeout) {
        print2("[FAILED] Timeout waiting for counter to reach 0\n");
        return;
    }

    /* 停止定时器 */
    WRITE_32(PRIVATE_TIMER_CTLR, 0x00000000);

    print2("[PASSED] uc_kn_4_5: Private timer interrupt test completed successfully\n");
}
