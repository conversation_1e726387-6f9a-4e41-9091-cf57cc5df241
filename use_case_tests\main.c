/*
 * 测试用例执行主函数
 */

#include "gic.h"

#include "uc-st-1.h" /* 引入测试用例函数声明 */
#include "uc-kn-2.h"
#include "uc-kn-3.h"
#include "uc-st-1.h"

#define APB_UART0_BASE 0xA0000000

void apb_uart_init(void)
{
    /* *(volatile uint32_t *)(BASE + 偏移) = 值 */
    *((volatile unsigned int *)(APB_UART0_BASE + 0x0C)) = 0x83;
    *((volatile unsigned int *)(APB_UART0_BASE + 0x00)) = 0x8B;   /* 400 MHz, 38400 */
    *((volatile unsigned int *)(APB_UART0_BASE + 0x04)) = 0x02;
    *((volatile unsigned int *)(APB_UART0_BASE + 0x0C)) = 0x03;
    *((volatile unsigned int *)(APB_UART0_BASE + 0x04)) = 0x00;   /* 2 */
    *((volatile unsigned int *)(APB_UART0_BASE + 0x08)) = 0x07;
}

int main(void)
{
    /* GIC 初始化 */
    gic_init();
    // apb_uart_init(); /* 目前 QEMU 初始化 UART 后无法正确输出 */
    
    /* 测试开始 */
    // uc_kn_2_1();
    // uc_kn_2_2();

    // uc_kn_3_1();
    // uc_kn_3_2();
    // uc_kn_3_3();
    // uc_kn_3_4();
    // uc_kn_3_5();
    // uc_kn_3_6();

    // uc_st_1_1();
    // uc_st_1_2();
    uc_st_1_3();
    uc_st_1_4();
    uc_st_1_5();
    uc_st_1_6();
    uc_st_1_7();
    uc_st_1_8();
    uc_st_1_9();
    uc_st_1_10();
    uc_st_1_11();

    /* 测试结束 */

    return 0;
}
