/*
 * A9MPCore 看门狗定时器模块测试用例实现
 *
 * 测试用例范围：UC-KN-5-1 ~ UC-KN-5-9
 */

#include "uc-kn-5.h"

#include "types.h"
#include "bsp_print.h"  /* 串口输出接口 */

/* A9MPCore 看门狗定时器寄存器定义 */
#define PERIPHBASE                      0x3FFF0000
#define WDT_BASE                        (PERIPHBASE + 0x620)
#define WDT_LOAD                        (WDT_BASE + 0x00)
#define WDT_COUNTER                     (WDT_BASE + 0x04)
#define WDT_CTRL                        (WDT_BASE + 0x08)
#define WDT_ISR                         (WDT_BASE + 0x0C)
#define WDT_RESET                       (WDT_BASE + 0x10)
#define WDT_DISABLE                     (WDT_BASE + 0x14)

/* 看门狗定时器寄存器复位值 */
#define WDT_LOAD_RESET                  0x00000000U
#define WDT_COUNTER_RESET               0x00000000U
#define WDT_CTRL_RESET                  0x00000000U
#define WDT_ISR_RESET                   0x00000000U
#define WDT_RESET_RESET                 0x00000000U

/* 测试用值 */
#define TEST_LOAD_VAL                   0x00100000U
#define TEST_CTRL_VAL                   0x0000000FU

/* 控制寄存器位定义 */
#define WDT_ENABLE_BIT                  (1 << 0)
#define WDT_AUTO_RELOAD_BIT             (1 << 1)
#define WDT_IRQ_ENABLE_BIT              (1 << 2)
#define WDT_WATCHDOG_MODE_BIT           (1 << 3)

/* 中断状态寄存器位定义 */
#define WDT_EVENT_FLAG_BIT              (1 << 0)

/* 复位状态寄存器位定义 */
#define WDT_RESET_FLAG_BIT              (1 << 0)

/* 禁用序列值 */
#define WDT_DISABLE_SEQ1                0x12345678U
#define WDT_DISABLE_SEQ2                0x87654321U

/* 简单延时函数 */
static void simple_delay(uint32_t count) {
    volatile uint32_t i;
    for (i = 0; i < count; i++) {
        /* 空循环 */
    }
}

void uc_kn_5_1(void)
{
    print2("[INFO] uc_kn_5_1: Starting watchdog timer registers test\n");

    uint32_t val;

    /* 验证可读寄存器复位值 */
    val = READ_32(WDT_LOAD);
    if (val != WDT_LOAD_RESET) {
        print2("[FAILED] LOAD reset mismatch: read=0x%x, exp=0x%x\n",
               val, WDT_LOAD_RESET);
        return;
    }

    val = READ_32(WDT_COUNTER);
    if (val != WDT_COUNTER_RESET) {
        print2("[FAILED] COUNTER reset mismatch: read=0x%x, exp=0x%x\n",
               val, WDT_COUNTER_RESET);
        return;
    }

    val = READ_32(WDT_CTRL);
    if (val != WDT_CTRL_RESET) {
        print2("[FAILED] CTRL reset mismatch: read=0x%x, exp=0x%x\n",
               val, WDT_CTRL_RESET);
        return;
    }

    val = READ_32(WDT_ISR);
    if (val != WDT_ISR_RESET) {
        print2("[FAILED] ISR reset mismatch: read=0x%x, exp=0x%x\n",
               val, WDT_ISR_RESET);
        return;
    }

    val = READ_32(WDT_RESET);
    if (val != WDT_RESET_RESET) {
        print2("[FAILED] RESET reset mismatch: read=0x%x, exp=0x%x\n",
               val, WDT_RESET_RESET);
        return;
    }

    /* 向所有寄存器写入合法值 */
    WRITE_32(WDT_LOAD, TEST_LOAD_VAL);
    WRITE_32(WDT_CTRL, TEST_CTRL_VAL);
    WRITE_32(WDT_ISR, 0x1);  /* 清除中断标志 */

    /* 写入后再次读取，确认可写可读寄存器值为刚才写入值 */
    val = READ_32(WDT_LOAD);
    if (val != TEST_LOAD_VAL) {
        print2("[FAILED] LOAD write/read mismatch: read=0x%x, exp=0x%x\n",
               val, TEST_LOAD_VAL);
        return;
    }

    val = READ_32(WDT_CTRL);
    if (val != TEST_CTRL_VAL) {
        print2("[FAILED] CTRL write/read mismatch: read=0x%x, exp=0x%x\n",
               val, TEST_CTRL_VAL);
        return;
    }

    /* 验证不可读寄存器是否通过QEMU日志反馈读取无效 */
    print2("[INFO] Checking QEMU log for invalid-read entries on write-only registers\n");
    print2("[PASSED] uc_kn_5_1: Watchdog timer registers test completed successfully\n");
}

void uc_kn_5_2(void)
{
    print2("[INFO] uc_kn_5_2: Starting watchdog timer load register test\n");

    uint32_t load_val = 0x00080000;  /* 加载值 */
    uint32_t counter_val, prev_counter;
    uint32_t timeout_count = 0;
    const uint32_t max_timeout = 10000;

    /* 清除看门狗定时器控制寄存器的定时器使能位、看门狗模式位 */
    WRITE_32(WDT_CTRL, 0x00000000);

    /* 设置看门狗定时器的加载寄存器的值为load_val */
    WRITE_32(WDT_LOAD, load_val);

    /* 设置定时器使能位 */
    WRITE_32(WDT_CTRL, WDT_ENABLE_BIT);

    /* 不断读取看门狗定时器的计数器值，观察到其不断减少直到定时器值为0 */
    print2("[INFO] Monitoring counter decreasing from load_val=0x%x...\n", load_val);

    prev_counter = READ_32(WDT_COUNTER);
    
    while (timeout_count < max_timeout) {
        counter_val = READ_32(WDT_COUNTER);
        
        if (counter_val > prev_counter) {
            print2("[FAILED] Counter should decrease: prev=0x%x, curr=0x%x\n",
                   prev_counter, counter_val);
            return;
        }
        
        if (counter_val == 0) {
            print2("[INFO] Counter reached 0 as expected\n");
            break;
        }
        
        prev_counter = counter_val;
        timeout_count++;
        simple_delay(100);
    }

    if (timeout_count >= max_timeout) {
        print2("[FAILED] Timeout waiting for counter to reach 0\n");
        return;
    }

    /* 停止定时器 */
    WRITE_32(WDT_CTRL, 0x00000000);

    print2("[PASSED] uc_kn_5_2: Watchdog timer load register test completed successfully\n");
}

void uc_kn_5_3(void)
{
    print2("[INFO] uc_kn_5_3: Starting watchdog timer counter register test\n");

    uint32_t load_val = 0x00040000;  /* 加载值 */
    uint32_t counter_val, prev_counter;
    uint32_t timeout_count = 0;
    const uint32_t max_timeout = 15000;
    uint32_t reload_count = 0;

    /* 清除看门狗定时器控制寄存器的定时器使能位、看门狗模式位 */
    WRITE_32(WDT_CTRL, 0x00000000);

    /* 设置看门狗定时器的加载寄存器的值为load_val */
    WRITE_32(WDT_LOAD, load_val);

    /* 设置定时器使能位、自动加载使能位 */
    WRITE_32(WDT_CTRL, WDT_ENABLE_BIT | WDT_AUTO_RELOAD_BIT);

    /* 不断读取看门狗定时器的计数器值，观察到其不断减少且当计数值为0时会自动加载load_val自动重新计数 */
    print2("[INFO] Monitoring counter with auto-reload (load_val=0x%x)...\n", load_val);

    prev_counter = READ_32(WDT_COUNTER);
    
    while (timeout_count < max_timeout && reload_count < 2) {
        counter_val = READ_32(WDT_COUNTER);
        
        /* 检测自动重载事件 */
        if (counter_val > prev_counter && prev_counter < (load_val / 10)) {
            reload_count++;
            print2("[INFO] Auto-reload detected #%d: prev=0x%x, curr=0x%x\n",
                   reload_count, prev_counter, counter_val);
        }
        
        /* 验证计数器在正常递减 */
        if (counter_val > prev_counter && prev_counter >= (load_val / 10)) {
            print2("[FAILED] Counter should decrease: prev=0x%x, curr=0x%x\n",
                   prev_counter, counter_val);
            return;
        }
        
        prev_counter = counter_val;
        timeout_count++;
        simple_delay(50);
    }

    if (reload_count < 2) {
        print2("[FAILED] Auto-reload not observed (reload_count=%d)\n", reload_count);
        return;
    }

    /* 停止定时器 */
    WRITE_32(WDT_CTRL, 0x00000000);

    print2("[PASSED] uc_kn_5_3: Watchdog timer counter register test completed successfully\n");
}

void uc_kn_5_4(void)
{
    print2("[INFO] uc_kn_5_4: Starting watchdog timer interrupt status register test\n");

    uint32_t load_val = 0x00020000;  /* 加载值 */
    uint32_t counter_val;
    uint32_t event_flag;
    uint32_t timeout_count = 0;
    const uint32_t max_timeout = 12000;

    /* 清除看门狗定时器控制寄存器的定时器使能位、看门狗模式位 */
    WRITE_32(WDT_CTRL, 0x00000000);

    /* 设置看门狗定时器的加载寄存器的值为load_val */
    WRITE_32(WDT_LOAD, load_val);

    /* 清除中断状态 */
    WRITE_32(WDT_ISR, 0x1);

    /* 设置定时器使能位 */
    WRITE_32(WDT_CTRL, WDT_ENABLE_BIT);

    /* 不断读取看门狗定时器的计数器值，观察到其不断减少且当计数值为0时，中断状态寄存器的事件标志位为1 */
    print2("[INFO] Monitoring counter and event flag (load_val=0x%x)...\n", load_val);

    while (timeout_count < max_timeout) {
        counter_val = READ_32(WDT_COUNTER);
        event_flag = READ_32(WDT_ISR) & WDT_EVENT_FLAG_BIT;

        if (counter_val > 0) {
            /* 当计数器值大于0时，确认事件标志位为0 */
            if (event_flag != 0) {
                print2("[FAILED] Event flag should be 0 when counter(0x%x) > 0: flag=0x%x\n",
                       counter_val, event_flag);
                return;
            }
        } else {
            /* 当计数器值为0时，确认事件标志位为1 */
            if (event_flag == 0) {
                print2("[FAILED] Event flag should be 1 when counter=0: flag=0x%x\n",
                       event_flag);
                return;
            }

            print2("[INFO] Event flag correctly set: counter=0x%x, flag=0x%x\n",
                   counter_val, event_flag);
            break;
        }

        timeout_count++;
        simple_delay(50);
    }

    if (timeout_count >= max_timeout) {
        print2("[FAILED] Timeout waiting for counter to reach 0\n");
        return;
    }

    /* 停止定时器 */
    WRITE_32(WDT_CTRL, 0x00000000);

    print2("[PASSED] uc_kn_5_4: Watchdog timer interrupt status register test completed successfully\n");
}

void uc_kn_5_5(void)
{
    print2("[INFO] uc_kn_5_5: Starting watchdog timer interrupt test\n");

    uint32_t load_val = 0x00030000;  /* 加载值 */
    uint32_t counter_val;
    uint32_t event_flag;
    uint32_t timeout_count = 0;
    const uint32_t max_timeout = 15000;

    /* 清除看门狗定时器控制寄存器的定时器使能位、看门狗模式位 */
    WRITE_32(WDT_CTRL, 0x00000000);

    /* 设置看门狗定时器的加载寄存器的值为load_val */
    WRITE_32(WDT_LOAD, load_val);

    /* 清除中断状态 */
    WRITE_32(WDT_ISR, 0x1);

    /* 设置定时器使能位、中断使能位 */
    WRITE_32(WDT_CTRL, WDT_ENABLE_BIT | WDT_IRQ_ENABLE_BIT);

    print2("[INFO] Watchdog timer started with interrupt enabled (load_val=0x%x)\n", load_val);
    print2("[INFO] Note: Check interrupt system for IRQ 30 (Watchdog Timer) when event occurs\n");

    /* 不断读取看门狗定时器的计数器值和中断状态寄存器的事件标志位 */
    while (timeout_count < max_timeout) {
        counter_val = READ_32(WDT_COUNTER);
        event_flag = READ_32(WDT_ISR) & WDT_EVENT_FLAG_BIT;

        if (counter_val > 0) {
            /* 当计数器值大于0时，确认事件标志位为0 */
            if (event_flag != 0) {
                print2("[FAILED] Event flag should be 0 when counter(0x%x) > 0: flag=0x%x\n",
                       counter_val, event_flag);
                return;
            }
        } else {
            /* 当计数器值为0时，确认事件标志位为1且中断系统触发相应中断 */
            if (event_flag == 0) {
                print2("[FAILED] Event flag should be 1 when counter=0: flag=0x%x\n",
                       event_flag);
                return;
            }

            print2("[INFO] Interrupt event triggered: counter=0x%x, flag=0x%x\n",
                   counter_val, event_flag);
            print2("[INFO] Watchdog Timer IRQ 30 should be triggered in interrupt system\n");

            /* 执行到此处说明中断返回 */
            print2("[INFO] Interrupt returned\n");
            break;
        }

        timeout_count++;
        simple_delay(50);
    }

    if (timeout_count >= max_timeout) {
        print2("[FAILED] Timeout waiting for counter to reach 0\n");
        return;
    }

    /* 停止定时器 */
    WRITE_32(WDT_CTRL, 0x00000000);

    print2("[PASSED] uc_kn_5_5: Watchdog timer interrupt test completed successfully\n");
}

void uc_kn_5_6(void)
{
    print2("[INFO] uc_kn_5_6: Starting watchdog timer reset test with reset flag check\n");

    uint32_t load_val = 0x00010000;  /* 较小的加载值，快速触发复位 */
    uint32_t reset_flag;

    /* 检查是否因看门狗复位而启动 */
    reset_flag = READ_32(WDT_RESET) & WDT_RESET_FLAG_BIT;
    
    if (reset_flag) {
        print2("[INFO] System was reset by watchdog, reset flag=0x%x\n", reset_flag);
        
        /* 向看门狗定时器的复位状态寄存器写入1，确认复位标志被清除 */
        WRITE_32(WDT_RESET, 0x1);
        
        reset_flag = READ_32(WDT_RESET) & WDT_RESET_FLAG_BIT;
        if (reset_flag != 0) {
            print2("[FAILED] Reset flag not cleared: flag=0x%x\n", reset_flag);
            return;
        }
        
        print2("[INFO] Reset flag successfully cleared\n");
        print2("[PASSED] uc_kn_5_6: Watchdog timer reset test completed successfully\n");
        return;
    }

    /* 清除看门狗定时器控制寄存器的定时器使能位 */
    WRITE_32(WDT_CTRL, 0x00000000);

    /* 设置看门狗定时器的加载寄存器的值为load_val */
    WRITE_32(WDT_LOAD, load_val);

    /* 设置定时器使能位、看门狗模式位 */
    WRITE_32(WDT_CTRL, WDT_ENABLE_BIT | WDT_WATCHDOG_MODE_BIT);

    print2("[INFO] Watchdog timer started in watchdog mode (load_val=0x%x)\n", load_val);
    print2("[INFO] Waiting for watchdog reset...\n");

    /* 不断读取看门狗定时器的计数器值，观察到其不断减少且当计数值为0时，系统进行复位 */
    while (1) {
        uint32_t counter_val = READ_32(WDT_COUNTER);
        print2("[INFO] Counter: 0x%x\n", counter_val);
        simple_delay(1000);
        /* 等待看门狗复位系统 */
    }
}

void uc_kn_5_7(void)
{
    print2("[INFO] uc_kn_5_7: Starting watchdog timer reset test\n");

    uint32_t load_val = 0x00008000;  /* 较小的加载值，快速触发复位 */

    /* 清除看门狗定时器控制寄存器的定时器使能位 */
    WRITE_32(WDT_CTRL, 0x00000000);

    /* 设置看门狗定时器的加载寄存器的值为load_val */
    WRITE_32(WDT_LOAD, load_val);

    /* 设置定时器使能位、看门狗模式位 */
    WRITE_32(WDT_CTRL, WDT_ENABLE_BIT | WDT_WATCHDOG_MODE_BIT);

    print2("[INFO] Watchdog timer started in watchdog mode (load_val=0x%x)\n", load_val);
    print2("[INFO] Waiting for watchdog reset...\n");

    /* 不断读取看门狗定时器的计数器值，观察到其不断减少且当计数值为0时，系统进行复位 */
    while (1) {
        uint32_t counter_val = READ_32(WDT_COUNTER);
        print2("[INFO] Counter: 0x%x\n", counter_val);
        simple_delay(1000);
        /* 等待看门狗复位系统 */
    }
}

void uc_kn_5_8(void)
{
    print2("[INFO] uc_kn_5_8: Starting watchdog timer disable sequence test\n");

    uint32_t load_val = 0x00100000;  /* 加载值 */
    uint32_t ctrl_val;

    /* 清除看门狗定时器控制寄存器的定时器使能位 */
    WRITE_32(WDT_CTRL, 0x00000000);

    /* 设置看门狗定时器的加载寄存器的值为load_val */
    WRITE_32(WDT_LOAD, load_val);

    /* 设置定时器使能位、看门狗模式位 */
    WRITE_32(WDT_CTRL, WDT_ENABLE_BIT | WDT_WATCHDOG_MODE_BIT);

    /* 读取看门狗模式位，确认目前看门狗定时器处于看门狗模式 */
    ctrl_val = READ_32(WDT_CTRL);
    if ((ctrl_val & WDT_WATCHDOG_MODE_BIT) == 0) {
        print2("[FAILED] Watchdog mode not set: ctrl=0x%x\n", ctrl_val);
        return;
    }
    print2("[INFO] Watchdog mode confirmed: ctrl=0x%x\n", ctrl_val);

    /* 写入特定禁用序列（依次写0x12345678然后写入0x87654321）到禁用寄存器 */
    WRITE_32(WDT_DISABLE, WDT_DISABLE_SEQ1);
    WRITE_32(WDT_DISABLE, WDT_DISABLE_SEQ2);

    /* 确认写入后看门狗定时器控制寄存器的看门狗模式为0 */
    ctrl_val = READ_32(WDT_CTRL);
    if ((ctrl_val & WDT_WATCHDOG_MODE_BIT) != 0) {
        print2("[FAILED] Watchdog mode not disabled after correct sequence: ctrl=0x%x\n", ctrl_val);
        return;
    }

    print2("[INFO] Watchdog mode successfully disabled: ctrl=0x%x\n", ctrl_val);

    /* 停止定时器 */
    WRITE_32(WDT_CTRL, 0x00000000);

    print2("[PASSED] uc_kn_5_8: Watchdog timer disable sequence test completed successfully\n");
}

void uc_kn_5_9(void)
{
    print2("[INFO] uc_kn_5_9: Starting watchdog timer incorrect disable sequence test\n");

    uint32_t load_val = 0x00100000;  /* 加载值 */
    uint32_t ctrl_val;

    /* 清除看门狗定时器控制寄存器的定时器使能位 */
    WRITE_32(WDT_CTRL, 0x00000000);

    /* 设置看门狗定时器的加载寄存器的值为load_val */
    WRITE_32(WDT_LOAD, load_val);

    /* 设置定时器使能位、看门狗模式位 */
    WRITE_32(WDT_CTRL, WDT_ENABLE_BIT | WDT_WATCHDOG_MODE_BIT);

    /* 读取看门狗模式位，确认目前看门狗定时器处于看门狗模式 */
    ctrl_val = READ_32(WDT_CTRL);
    if ((ctrl_val & WDT_WATCHDOG_MODE_BIT) == 0) {
        print2("[FAILED] Watchdog mode not set: ctrl=0x%x\n", ctrl_val);
        return;
    }
    print2("[INFO] Watchdog mode confirmed: ctrl=0x%x\n", ctrl_val);

    /* 测试错误的禁用序列 */
    print2("[INFO] Testing incorrect disable sequences...\n");

    /* 错误序列1：写入错误的值 */
    WRITE_32(WDT_DISABLE, 0x11111111);
    WRITE_32(WDT_DISABLE, 0x22222222);

    ctrl_val = READ_32(WDT_CTRL);
    if ((ctrl_val & WDT_WATCHDOG_MODE_BIT) == 0) {
        print2("[FAILED] Watchdog mode incorrectly disabled by wrong sequence: ctrl=0x%x\n", ctrl_val);
        return;
    }

    /* 错误序列2：在正确序列中额外进行写入操作 */
    WRITE_32(WDT_DISABLE, WDT_DISABLE_SEQ1);
    WRITE_32(WDT_DISABLE, 0x99999999);  /* 额外写入 */
    WRITE_32(WDT_DISABLE, WDT_DISABLE_SEQ2);

    ctrl_val = READ_32(WDT_CTRL);
    if ((ctrl_val & WDT_WATCHDOG_MODE_BIT) == 0) {
        print2("[FAILED] Watchdog mode incorrectly disabled by interrupted sequence: ctrl=0x%x\n", ctrl_val);
        return;
    }

    /* 错误序列3：颠倒顺序 */
    WRITE_32(WDT_DISABLE, WDT_DISABLE_SEQ2);
    WRITE_32(WDT_DISABLE, WDT_DISABLE_SEQ1);

    ctrl_val = READ_32(WDT_CTRL);
    if ((ctrl_val & WDT_WATCHDOG_MODE_BIT) == 0) {
        print2("[FAILED] Watchdog mode incorrectly disabled by reversed sequence: ctrl=0x%x\n", ctrl_val);
        return;
    }

    print2("[INFO] All incorrect sequences properly rejected, watchdog mode remains: ctrl=0x%x\n", ctrl_val);

    /* 停止定时器 */
    WRITE_32(WDT_CTRL, 0x00000000);

    print2("[PASSED] uc_kn_5_9: Watchdog timer incorrect disable sequence test completed successfully\n");
}
