#include "soc502_parameter.h"
#include "types.h"
#include "irq_gic.h"
#include "uart.h"
#include "math.h"
#include "stdio.h"
#include "pic.h"
#include "perf.h"

#define OUT_TUBE
#define DEBUG
// #define QEMU_ENV

/* 初始化回退标志为 0 */
int global_flag = 0;

void outbyte(int byte);
void apb_uart_init(void);
void delayus(u32 us);
typedef unsigned char U8;
typedef unsigned int U32;
typedef  char I8;
typedef  int I32;

void printDouble(double v,int decDigits)
{
  int i = 1;
  int intPart, fractPart;
  int digits, digits0, bit0;
  int decimalDigits = decDigits+1;
  // print2("digits %d\n",decimalDigits);
  for (digits = decimalDigits;digits!=0; i*=10, digits--);
  intPart = (int)v;
  fractPart = (int)((v-(double)(int)v)*i);
  if(fractPart < 0) fractPart *= -1;

  if((fractPart%10)>4)
  {
    if (fractPart==(i-1))
    {
      if(v<0.0)
        intPart--;
      else
        intPart++;
      fractPart=0;
    }
    else
    {
      fractPart+=10;
    }
  }
  fractPart/=10;
  decimalDigits = decDigits;
  digits0 = 1;
  bit0 = decimalDigits;
  for (digits = decimalDigits;digits!=0; i*=10, digits--)
  {
    digits0 *= 10;
    bit0--;
    if((fractPart/digits0)==0) break;
  };

  if ((intPart==0)&&(v<0.0))
    print2("-0");
  else
    print2("%i", intPart);
  if(decDigits>0) print2(".");
  if (bit0>0)
  {
    for(i=0;i<bit0;i++) print2("0");
  }
  if(decDigits>0) print2("%i", fractPart);
}

int _write (int fd, char *pBuffer, int size)  
{  
    for (int i = 0; i < size; i++)  
    {  
        outbyte(pBuffer);
    }  
    return size;  
}

unsigned int MoniterRead(void)
{
   unsigned int tmp,tmp2;
    for (;;)
    {
        tmp = r32(APB_UART1_BASE + 0x08);
        if ( 0x00000004 == (tmp & 0x00000004 ))
        {
            tmp2 = r32(APB_UART1_BASE + 0x00);
	     	//print2("\r\nUARTSR:0x%x", tmp);
            return( tmp2 );	
        }
    }
}

static volatile uint32_t sink;

static void test_loop1(void)
{
    for (uint32_t i = 0; i < 1000000; ++i) {
        sink += i;
    }
}

void print2_hex(unsigned int val) {
    // 缓冲区足以容纳 "0x" 前缀, 8个16进制数字, 以及一个空终止符 '\0'
    char hex_str[11];
    const char *hex_digits = "0123456789ABCDEF";

    hex_str[0] = '0';
    hex_str[1] = 'x';
    hex_str[10] = '\0'; // 在字符串末尾设置空终止符

    // 循环8次，对应8个16进制数字
    for (int i = 0; i < 8; i++) {
        // 计算当前要提取的“四位组”(nibble)的位移量。
        // i=0时, 提取最高位的四位组 (位移28)
        // i=7时, 提取最低位的四位组 (位移0)
        int shift = 28 - (i * 4);
        
        // 1. 右移整数，将目标四位组移动到最低位
        // 2. 使用 & 0xF 操作隔离出这个四位组的值 (0-15)
        unsigned int nibble = (val >> shift) & 0xF;
        
        // 从查找表中获取对应的16进制字符，并放入字符串缓冲区。
        // 16进制数字从缓冲区的第2个索引开始存放。
        hex_str[i + 2] = hex_digits[nibble];
    }

    // 一次性打印整个构建好的字符串
    print2(hex_str);
}

void undefined_instruction_test(void) {
  print2("\n--- Detailed Assembly Test Start ---\n");

    // 准备用于加载的内存
    volatile unsigned int dummy_memory[4] = { 0xDEADBEEF, 0xCAFECAFE, 0x12345678, 0x87654321 };
    volatile unsigned int *p_mem = dummy_memory;
    volatile unsigned int original_cpsr, svc_cpsr;

    // --- 步骤 1: 读取当前CPSR ---
    print2("Step 1: Reading current CPSR...\n");
    __asm__ __volatile__ (
        "mrs %[cpsr_val], cpsr\n\t"
        : [cpsr_val] "=r" (original_cpsr)
    );
    print2("  Original CPSR is: ");
    print2_hex(original_cpsr); // 打印原始CPSR的值
    print2("\n");

    // --- 步骤 2: 准备切换到SVC模式 ---
    print2("Step 2: Preparing to switch to SVC mode...\n");
    svc_cpsr = (original_cpsr & ~0x1F) | 0x13; // 清除模式位，设置为SVC (0b10011)
    print2("  Target SVC CPSR will be: ");
    print2_hex(svc_cpsr);
    print2("\n");

    // --- 步骤 3: 正式切换到SVC模式 ---
    print2("Step 3: Executing MSR to switch to SVC mode...\n");
    __asm__ __volatile__ (
        "msr cpsr_c, %[new_cpsr]\n\t"
        :
        : [new_cpsr] "r" (svc_cpsr)
    );
    print2("  Successfully switched to SVC mode.\n");

    // --- 步骤 4: 验证当前模式 (在SVC模式下读取CPSR) ---
    unsigned int current_cpsr_in_svc;
    print2("Step 4: Verifying current mode is SVC...\n");
    __asm__ __volatile__ (
        "mrs %[cpsr_val], cpsr\n\t"
        : [cpsr_val] "=r" (current_cpsr_in_svc)
    );
    print2("  CPSR read from within SVC mode: ");
    print2_hex(current_cpsr_in_svc);
    print2("\n");
    if ((current_cpsr_in_svc & 0x1F) == 0x13) {
        print2("  Verification PASSED. Current mode is SVC.\n");
    } else {
        print2("  Verification FAILED. Current mode is NOT SVC. Aborting test.\n");
        goto test_end; // 如果模式不对，直接跳到结尾
    }

    // --- 步骤 5: 准备执行核心测试指令 ---
    print2("Step 5: Preparing to execute 'ldm r0!, {lr}^'...\n");
    print2("  Address to load from (r0) will be: ");
    print2_hex((unsigned int)p_mem);
    print2("\n");
    print2("  Value at that address is: ");
    print2_hex(*p_mem);
    print2("\n");
    print2("  Connect GDB now! Breakpoint at Undefined Instruction vector.\n");
    print2("  >>> EXECUTING THE TARGET INSTRUCTION NOW <<<\n");

    // --- 步骤 6: 执行核心测试指令 ---
    __asm__ __volatile__ (
        "mov r0, %[ptr]\n\t"
        "ldm r0!, {lr}^"
        : [ptr] "+r" (p_mem)
        :
        : "r0", "lr", "memory"
    );

    // 如果程序能执行到这里，说明没有触发异常
    print2("\n  >>> INSTRUCTION EXECUTED SUCCESSFULLY! <<<\n");
    print2("  No immediate exception was triggered.\n");
    print2("  The value of p_mem is now: ");
    print2_hex((unsigned int)p_mem); // 检查r0是否自增了
    print2("\n");


test_end:
    // --- 步骤 7: 恢复原始模式 ---
    print2("Step 7: Restoring original CPSR...\n");
    __asm__ __volatile__ (
        "msr cpsr_c, %[orig_cpsr]\n\t"
        :
        : [orig_cpsr] "r" (original_cpsr)
    );

    print2("--- Detailed Assembly Test End ---\n");
}

/*
 * A9MPCore 全局定时器模块测试用例实现
 *
 * 测试用例范围：UC-KN-3-1 ~ UC-KN-3-6
 */

 #define READ_8(addr)                (*(volatile uint8_t*)(addr))
#define READ_16(addr)               (*(volatile uint16_t*)(addr))
#define READ_32(addr)               (*(volatile uint32_t*)(addr))
#define WRITE_8(addr, value)        (*(volatile uint8_t*)(addr) = (uint8_t)(value))
#define WRITE_16(addr, value)       (*(volatile uint16_t*)(addr) = (uint16_t)(value))
#define WRITE_32(addr, value)       (*(volatile uint32_t*)(addr) = (value))

/* A9MPCore 全局定时器寄存器定义 */
#define PERIPHBASE                      0x3FFF0000
#define GLOBAL_TIMER_BASE               (PERIPHBASE + 0x200)
#define GLOBAL_TIMER_COUNTER_LOW        (GLOBAL_TIMER_BASE + 0x00)
#define GLOBAL_TIMER_COUNTER_HIGH       (GLOBAL_TIMER_BASE + 0x04)
#define GLOBAL_TIMER_CTLR               (GLOBAL_TIMER_BASE + 0x08)
#define GLOBAL_TIMER_ISR                (GLOBAL_TIMER_BASE + 0x0C)
#define GLOBAL_TIMER_CMP_LOW            (GLOBAL_TIMER_BASE + 0x10)
#define GLOBAL_TIMER_CMP_HIGH           (GLOBAL_TIMER_BASE + 0x14)
#define GLOBAL_TIMER_AUTO_INCR          (GLOBAL_TIMER_BASE + 0x18)

/* 全局定时器寄存器复位值 */
#define GLOBAL_TIMER_COUNTER_LOW_RESET      0x00000000U
#define GLOBAL_TIMER_COUNTER_HIGH_RESET     0x00000000U
#define GLOBAL_TIMER_CTLR_RESET             0x00000000U
#define GLOBAL_TIMER_ISR_RESET              0x00000000U
#define GLOBAL_TIMER_CMP_LOW_RESET          0x00000000U
#define GLOBAL_TIMER_CMP_HIGH_RESET         0x00000000U
#define GLOBAL_TIMER_AUTO_INCR_RESET        0x00000000U

/* 测试用值 */
#define TEST_COUNTER_LOW_VAL                0x12345678U
#define TEST_COUNTER_HIGH_VAL               0x9ABCDEF0U
#define TEST_CTLR_VAL                       0x0000000FU
#define TEST_CMP_LOW_VAL                    0x00100000U
#define TEST_CMP_HIGH_VAL                   0x00000000U
#define TEST_AUTO_INCR_VAL                  0x00050000U

/* 控制寄存器位定义 */
#define GTIMER_ENABLE_BIT                   (1 << 0)
#define GTIMER_COMP_ENABLE_BIT              (1 << 1)
#define GTIMER_IRQ_ENABLE_BIT               (1 << 2)
#define GTIMER_AUTO_INCR_BIT                (1 << 3)

/* 中断状态寄存器位定义 */
#define GTIMER_EVENT_FLAG_BIT               (1 << 0)

/* 全局定时器读取函数（处理64位读取的原子性） */
static uint64_t global_timer_read(void) {
    uint32_t high1, low, high2;

    do {
        high1 = READ_32(GLOBAL_TIMER_COUNTER_HIGH);
        low   = READ_32(GLOBAL_TIMER_COUNTER_LOW);
        high2 = READ_32(GLOBAL_TIMER_COUNTER_HIGH);
    } while (high1 != high2);

    return ((uint64_t)high1 << 32) | low;
}

/* 简单延时函数 */
static void simple_delay(uint32_t count) {
    volatile uint32_t i;
    for (i = 0; i < count; i++) {
        /* 空循环 */
    }
}
void uc_kn_3_5(void)
{
    print2("[INFO] uc_kn_3_5: Starting global timer event flag test\n");

    uint32_t cmp_val = 0x00800000;  /* 比较值 */
    uint64_t counter_val;
    uint32_t event_flag;
    uint32_t timeout_count = 0;
    const uint32_t max_timeout = 12000;

    /* 清除全局定时器控制寄存器的定时器使能位 */
    WRITE_32(GLOBAL_TIMER_CTLR, 0x00000000);

    /* 清零全局定时器的计数器寄存器高32位和低32位 */
    WRITE_32(GLOBAL_TIMER_COUNTER_LOW, 0x00000000);
    WRITE_32(GLOBAL_TIMER_COUNTER_HIGH, 0x00000000);

    /* 设置比较值寄存器为cmp_val */
    WRITE_32(GLOBAL_TIMER_CMP_LOW, cmp_val);
    WRITE_32(GLOBAL_TIMER_CMP_HIGH, 0x00000000);

    /* 清除中断状态 */
    WRITE_32(GLOBAL_TIMER_ISR, 0x1);

    /* 设置定时器使能位、比较使能位 */
    WRITE_32(GLOBAL_TIMER_CTLR, GTIMER_ENABLE_BIT | GTIMER_COMP_ENABLE_BIT);

    /* 不断读取全局定时器的计数器值和中断状态寄存器的事件标志位 */
    print2("[INFO] Monitoring counter and event flag (cmp_val=0x%x)...\n", cmp_val);

    while (timeout_count < max_timeout) {
        counter_val = global_timer_read();
        event_flag = READ_32(GLOBAL_TIMER_ISR) & GTIMER_EVENT_FLAG_BIT;

        if (counter_val < cmp_val) {
            /* 当计数器寄存器值小于比较值时，确认事件标志位为0 */
            if (event_flag != 0) {
                print2("[FAILED] Event flag should be 0 when counter(0x%x%x) < compare(0x%x): flag=0x%x\n",
                       (uint32_t)(counter_val >> 32), (uint32_t)counter_val, cmp_val, event_flag);
                return;
            }
        } else {
            /* 当计数器寄存器值大于比较值时，确认事件标志位为1 */
            if (event_flag == 0) {
                print2("[FAILED] Event flag should be 1 when counter(0x%x%x) >= compare(0x%x): flag=0x%x\n",
                       (uint32_t)(counter_val >> 32), (uint32_t)counter_val, cmp_val, event_flag);
                return;
            }

            print2("[INFO] Event flag correctly set: counter=0x%x%x, compare=0x%x, flag=0x%x\n",
                   (uint32_t)(counter_val >> 32), (uint32_t)counter_val, cmp_val, event_flag);
            break;
        }

        timeout_count++;
        simple_delay(50);
    }

    if (timeout_count >= max_timeout) {
        print2("[FAILED] Timeout waiting for counter to reach compare value\n");
        return;
    }

    /* 停止定时器 */
    WRITE_32(GLOBAL_TIMER_CTLR, 0x00000000);

    print2("[PASSED] uc_kn_3_5: Global timer event flag test completed successfully\n");
}
void c_main(void)
{
    u32 addr,data,cb;
    u32 tmp,j,i;

      /* 外设初始化 */
    apb_uart_init();
    gic_init();

    uc_kn_3_5();
	
    /* 性能测试组件初始化 */
    // pmu_enable_ccnt();

    /* 性能测试 */
    // uint32_t cyc = perf_time_ns(test_loop1);

// #ifdef QEMU_ENV
//     print2("test_loop1 in QEMU costs: %uns\n", cyc);
// #else
//     if (global_flag == 0) {
//         cyc = (uint64_t)cyc * 5ULL / 2ULL;
//         print2("test_loop1 on real chip costs: %uns\n", cyc);
//     } else {
//         print2("test_loop1 on real chip costs: %uns\n", cyc);
//     }
    
// #endif
    
	while(1);
}

void apb_uart_init(void)
{
	w32(APB_UART1_BASE + 0x0c, 0x83);
	w32(APB_UART1_BASE + 0x00, 0x8B);//400MHz,38400
	w32(APB_UART1_BASE + 0x04, 0x2);
	w32(APB_UART1_BASE + 0x0c, 0x03);
	w32(APB_UART1_BASE + 0x04, 0x01); //2
	w32(APB_UART1_BASE + 0x08,0x07);

	w32(APB_UART0_BASE + 0x0c, 0x83);
	w32(APB_UART0_BASE + 0x00, 0x8B);//400MHz,38400
	w32(APB_UART0_BASE + 0x04, 0x2);
	w32(APB_UART0_BASE + 0x0c, 0x03);
	w32(APB_UART0_BASE + 0x04, 0x00); //2
	w32(APB_UART0_BASE + 0x08,0x07);
}
void outbyte(int byte)
{
//  while( !(r32(APB_UART0_BASE + 0x14) & 0x20) ) asm("nop");
//	w32(APB_UART0_BASE + 0x00, byte);
  while( !(r32(APB_UART1_BASE + 0x14) & 0x20) ) asm("nop");
	w32(APB_UART1_BASE + 0x00, byte);
}


//
// delay function use private timer
//

void SYS_Delay(u32 dtm)
{
	u32 tmold;
	u32 tmnew;
	u32 tmdiff;
	u32 cmpval;

        w32(A9_PRIVATE_BASE + 0x08, 0x00000000);
        w32(A9_PRIVATE_BASE + 0x00, 0xffffffff);
        w32(A9_PRIVATE_BASE + 0x04, 0xffffffff);
        w32(A9_PRIVATE_BASE + 0x08, (249<<8 | 0x1<<1 |0x1));

	tmold  = r32(A9_PRIVATE_BASE + 0x04); 	

	cmpval = dtm*2;
	tmdiff = 0;
	while ( tmdiff < cmpval)
	{
		tmnew  = r32(A9_PRIVATE_BASE + 0x04); 	

		if ( tmnew <=tmold ) 
		{
			tmdiff = tmold -tmnew;
		}
		else  
		{
			tmdiff = 0xffffffff - tmnew + tmold +1;  
		}
	}
}


u32 get_tb_start(void)
{
	u32 tmval;
        w32(A9_PRIVATE_BASE + 0x08, 0x00000000);
        w32(A9_PRIVATE_BASE + 0x00, 0xffffffff);
        w32(A9_PRIVATE_BASE + 0x04, 0xffffffff);
        w32(A9_PRIVATE_BASE + 0x08, (249<<8 | 0x1<<1 |0x1));//500MHz / 250 = 2MHz

	tmval  = r32(A9_PRIVATE_BASE + 0x04); 	
	return tmval;
}

u32 get_tb_end(void)
{
	u32 tmval;
	tmval = r32(A9_PRIVATE_BASE + 0x04);
	return tmval;
}
 u32 get_tb_diff(u32 tmold, u32 tmnew)
{
	u32 tmdiff;
	
	if ( tmnew <=tmold ) 
	{
		tmdiff = tmold -tmnew;
	}
	else  
	{
		tmdiff = 0xffffffff - tmnew + tmold +1;  
	}

	return tmdiff;
}
//
// delay function use global timer
//
#define TIMER_CLK       (500000000 / 1)
#define CLK_PER_US      (TIMER_CLK / 1000000) //--right

void delayus(u32 us)
{
        u32 timer_end;

        timer_end = us * CLK_PER_US;
        // reset timer values
        w32(A9_GLOBAL_TIMER_BASE + 0x08, 0x00000000);
        w32(A9_GLOBAL_TIMER_BASE + 0x00, 0x00000000);
        w32(A9_GLOBAL_TIMER_BASE + 0x04, 0x00000000);
        w32(A9_GLOBAL_TIMER_BASE + 0x10, 0x00000000);
        w32(A9_GLOBAL_TIMER_BASE + 0x14, 0x00000000);

        // clear status bit
        w32(A9_GLOBAL_TIMER_BASE + 0x0c, 0x00000001);

        // set the end value
        w32(A9_GLOBAL_TIMER_BASE + 0x10, timer_end);
        // start counter: single shot
        w32(A9_GLOBAL_TIMER_BASE + 0x08, 0x00000003);

        // waiting for counter reaching the end
        while(1) {
                if(r32(A9_GLOBAL_TIMER_BASE + 0x0c))
                        break;
        }
}

void delay_ms(u32 ms)
{
  delayus(ms*1000);
}

