/*
 * 片上 SRAM 及控制寄存器模块测试用例实现
 *
 * 测试用例范围：UC-ST-1-1 ~ UC-ST-1-5
 */

#include "uc-st-1.h"

#include "types.h"
#include "bsp_print.h"  /* 串口输出接口 */
#include "sram.h"

/* SRAM EDAC 校验算法 测试用 */
static uint8_t gen_edac(uint8_t data)
{
    /* 数据位提取 */
	uint8_t d0 = (data >> 0) & 1;
	uint8_t d1 = (data >> 1) & 1;
	uint8_t d2 = (data >> 2) & 1;
	uint8_t d3 = (data >> 3) & 1;
	uint8_t d4 = (data >> 4) & 1;
	uint8_t d5 = (data >> 5) & 1;
	uint8_t d6 = (data >> 6) & 1;
	uint8_t d7 = (data >> 7) & 1;

	/* EDAC 校验位生成 */
	uint8_t m0 = d0 ^ d2 ^ d3 ^ d5 ^ d6 ^ d7;
	uint8_t m1 = d0 ^ d1 ^ d2 ^ d3;
	uint8_t m2 = d0 ^ d4 ^ d5 ^ d6;
	uint8_t m3 = d1 ^ d2 ^ d4 ^ d5 ^ d7;
	uint8_t m4 = d1 ^ d3 ^ d4 ^ d6 ^ d7;

    return m0 | (m1 << 1) | (m2 << 2) | (m3 << 3) | (m4 << 4);
}

static uint64_t gen_sram_edac(uint64_t data) {
    uint64_t res = 0;
    for (int i = 0; i < 8; i++) {
        uint8_t byte = (data >> (i * 8)) & 0xFF;
        uint8_t code = gen_edac(byte);
        res |= (uint64_t)code << (i * 8);
    }
    return res;
}

/* SRAM 参数 */
#define SRAM_START_ADDR   0x40000000U
#define SRAM_END_ADDR     0x401FFFFFU
#define SRAM_WORD_COUNT   (((SRAM_END_ADDR - SRAM_START_ADDR) + 1) / sizeof(uint32_t))

void uc_st_1_1(void) {
    print2("[INFO] uc_st_1_1: Starting test\n");

    volatile uint32_t * const sram = (uint32_t *)SRAM_START_ADDR;
    uint32_t expected, actual;

    /* 写入并验证每个 32-bit 单元 */
    for (uint32_t i = 0; i < SRAM_WORD_COUNT; i++) {
        /* 测试模式：使用索引值作为数据 */
        expected = i;
        sram[i] = expected;

        /* 立刻读回并对比 */
        actual = sram[i];
        if (actual != expected) {
            print2(
                "[FAILED] uc_st_1_1 at word %u (addr=0x%x): wrote=0x%x, read=0x%x\n",
                i,
                (uint32_t)(SRAM_START_ADDR + i * sizeof(uint32_t)),
                expected,
                actual
            );
            return;
        }
    }

    print2(
        "[PASSED] uc_st_1_1: all %u words verified successfully (0x%x ~ 0x%x)\n",
        SRAM_WORD_COUNT,
        SRAM_START_ADDR,
        SRAM_END_ADDR
    );
}

/* 可读寄存器复位值否则为 0 */
#define SRAM_MEM_CONFIG_RESET                0x00000001U
#define SRAM_EDAC_BYPASS_DATAIN_RESET        0x00000000U
#define SRAM_ADDR_ONE_BIT_REG_RESET          0x40200010U
#define SRAM_ADDR_ONE_BIT_COUNT_RESET        0x00000000U
#define SRAM_ADDR_TWO_BIT_REG_RESET          0x40200020U
#define SRAM_EDAC_READBYPASS_DATA_LOW_RESET  0x00000000U
#define SRAM_EDAC_READBYPASS_DATA_HIGH_RESET 0x00000000U
#define SRAM_DATA_TWO_BIT_REG_RESET          0x00000000U
#define SRAM_EDAC_TWO_BIT_REG_RESET          0x00000000U
#define SRAM_BYTE_ERR_REG_RESET              0x00000000U

/* 测试写入值（合法值示例） */
#define MEM_CONFIG_TEST_VAL                  0x0000000FU
#define EDAC_BYPASS_DATAIN_TEST_VAL          0x0000001FU
#define ONE_BIT_COUNT_TEST_VAL               0xFFFFFFFFU /* 写清零 */
#define ONE_BIT_CLEAR_TEST_VAL               0x00000001U /* 写清零 */
#define TWO_BIT_CLEAR_TEST_VAL               0x00000001U
#define READ_BYPASS_ADDR_TEST_VAL            0x40200028U
#define WRITE_BYPASS_ADDR_TEST_VAL           0x40200050U

void uc_st_1_2(void) {
    print2("[INFO] uc_st_1_2: Starting SRAM controller registers test\n");
    
    uint32_t val;

    /* 验证所有可读寄存器是否等于复位值 */
    val = *(volatile uint32_t *)SRAM_MEM_CONFIG;
    if (val != SRAM_MEM_CONFIG_RESET) {
        print2("[FAILED] MEM_CONFIG reset mismatch: read=0x%x, exp=0x%x\n",
               val, SRAM_MEM_CONFIG_RESET);
        return;
    }
    val = *(volatile uint32_t *)SRAM_EDAC_BYPASS_DATAIN;
    if (val != SRAM_EDAC_BYPASS_DATAIN_RESET) {
        print2("[FAILED] EDAC_BYPASS_DATAIN reset mismatch: read=0x%x, exp=0x%x\n",
               val, SRAM_EDAC_BYPASS_DATAIN_RESET);
        return;
    }
    val = *(volatile uint32_t *)SRAM_ADDR_ONE_BIT_REG;
    if (val != SRAM_ADDR_ONE_BIT_REG_RESET) {
        print2("[FAILED] ADDR_ONE_BIT_REG reset mismatch: read=0x%x, exp=0x%x\n",
               val, SRAM_ADDR_ONE_BIT_REG_RESET);
        return;
    }
    val = *(volatile uint32_t *)SRAM_ADDR_ONE_BIT_COUNT;
    if (val != SRAM_ADDR_ONE_BIT_COUNT_RESET) {
        print2("[FAILED] ADDR_ONE_BIT_COUNT reset mismatch: read=0x%x, exp=0x%x\n",
               val, SRAM_ADDR_ONE_BIT_COUNT_RESET);
        return;
    }
    val = *(volatile uint32_t *)SRAM_ADDR_TWO_BIT_REG;
    if (val != SRAM_ADDR_TWO_BIT_REG_RESET) {
        print2("[FAILED] ADDR_TWO_BIT_REG reset mismatch: read=0x%x, exp=0x%x\n",
               val, SRAM_ADDR_TWO_BIT_REG_RESET);
        return;
    }
    val = *(volatile uint32_t *)SRAM_EDAC_READBYPASS_DATA_LOW;
    if (val != SRAM_EDAC_READBYPASS_DATA_LOW_RESET) {
        print2("[FAILED] READBYPASS_DATA_LOW reset mismatch: read=0x%x, exp=0x%x\n",
               val, SRAM_EDAC_READBYPASS_DATA_LOW_RESET);
        return;
    }
    val = *(volatile uint32_t *)SRAM_EDAC_READBYPASS_DATA_HIGH;
    if (val != SRAM_EDAC_READBYPASS_DATA_HIGH_RESET) {
        print2("[FAILED] READBYPASS_DATA_HIGH reset mismatch: read=0x%x, exp=0x%x\n",
               val, SRAM_EDAC_READBYPASS_DATA_HIGH_RESET);
        return;
    }
    val = *(volatile uint32_t *)SRAM_BYTE_ERR_REG;
    if (val != SRAM_BYTE_ERR_REG_RESET) {
        print2("[FAILED] BYTE_ERR_REG reset mismatch: read=0x%x, exp=0x%x\n",
               val, SRAM_BYTE_ERR_REG_RESET);
        return;
    }
    val = *(volatile uint32_t *)SRAM_DATA_TWO_BIT_REG;
    if (val != SRAM_DATA_TWO_BIT_REG_RESET) {
        print2("[FAILED] DATA_TWO_BIT_REG reset mismatch: read=0x%x, exp=0x%x\n",
               val, SRAM_DATA_TWO_BIT_REG_RESET);
        return;
    }
    val = *(volatile uint32_t *)SRAM_EDAC_TWO_BIT_REG;
    if (val != SRAM_EDAC_TWO_BIT_REG_RESET) {
        print2("[FAILED] EDAC_TWO_BIT_REG reset mismatch: read=0x%x, exp=0x%x\n",
               val, SRAM_EDAC_TWO_BIT_REG_RESET);
        return;
    }

    /* 查看不可读寄存器是否在 QEMU 日志中报告无效读取 */
    print2("[INFO] Checking QEMU log for invalid-read entries on read-only registers\n");

    /* 向所有寄存器写入合法值（无视权限） */
    *(volatile uint32_t *)SRAM_MEM_CONFIG             = MEM_CONFIG_TEST_VAL;
    *(volatile uint32_t *)SRAM_EDAC_BYPASS_DATAIN     = EDAC_BYPASS_DATAIN_TEST_VAL;
    *(volatile uint32_t *)SRAM_ADDR_ONE_BIT_REG       = ONE_BIT_COUNT_TEST_VAL;      /* RO */
    *(volatile uint32_t *)SRAM_ADDR_ONE_BIT_COUNT     = ONE_BIT_COUNT_TEST_VAL;
    *(volatile uint32_t *)SRAM_ADDR_TWO_BIT_REG       = ONE_BIT_COUNT_TEST_VAL;      /* RO */
    *(volatile uint32_t *)SRAM_EDAC_READBYPASS_DATA_LOW  = EDAC_BYPASS_DATAIN_TEST_VAL;/* RO */
    *(volatile uint32_t *)SRAM_EDAC_READBYPASS_DATA_HIGH = EDAC_BYPASS_DATAIN_TEST_VAL;/* RO */
    *(volatile uint32_t *)SRAM_ONE_BIT_CLEAR          = ONE_BIT_CLEAR_TEST_VAL;
    *(volatile uint32_t *)SRAM_TWO_BIT_CLEAR          = TWO_BIT_CLEAR_TEST_VAL;
    *(volatile uint32_t *)SRAM_BYTE_ERR_REG           = ONE_BIT_CLEAR_TEST_VAL;      /* RO */
    *(volatile uint32_t *)SRAM_EDAC_READ_BYPASS_ADDR    = READ_BYPASS_ADDR_TEST_VAL;
    *(volatile uint32_t *)SRAM_EDAC_WRITE_BYPASS_ADDR   = WRITE_BYPASS_ADDR_TEST_VAL;
    *(volatile uint32_t *)SRAM_DATA_TWO_BIT_REG       = ONE_BIT_CLEAR_TEST_VAL;      /* RO */
    *(volatile uint32_t *)SRAM_EDAC_TWO_BIT_REG       = ONE_BIT_CLEAR_TEST_VAL;      /* RO */
    
    /* 验证不可写寄存器是否在 QEMU 日志中报告无效写入 */
    print2("[INFO] Checking QEMU log for invalid-write entries on write-only registers\n");

    /* 再次读取并验证所有可写可读寄存器值 */
    val = *(volatile uint32_t *)SRAM_MEM_CONFIG;
    if (val != MEM_CONFIG_TEST_VAL) {
        print2("[FAILED] MEM_CONFIG RW mismatch: read=0x%x, exp=0x%x\n",
               val, MEM_CONFIG_TEST_VAL);
        return;
    }
    val = *(volatile uint32_t *)SRAM_EDAC_BYPASS_DATAIN;
    if (val != EDAC_BYPASS_DATAIN_TEST_VAL) {
        print2("[FAILED] EDAC_BYPASS_DATAIN RW mismatch: read=0x%x, exp=0x%x\n",
               val, EDAC_BYPASS_DATAIN_TEST_VAL);
        return;
    }
    val = *(volatile uint32_t *)SRAM_ADDR_ONE_BIT_COUNT;
    if (val != 0) { /* 写清零 */
        print2("[FAILED] ADDR_ONE_BIT_COUNT RW mismatch: read=0x%x, exp=0x%x\n",
               val, 0);
        return;
    }

    print2("[PASSED] uc_st_1_2: SRAM controller registers test completed successfully\n");
}

void uc_st_1_3(void) {
    print2("[INFO] uc_st_1_3: Starting SRAM EDAC error injection test\n");

    uint32_t cfg;
    uint32_t dummy;
    volatile uint32_t *addr = (uint32_t *)SRAM_START_ADDR;

    /* 读取 SRAM_MEM_CONFIG，确认 edac_en 位为 1 */
    cfg = *(volatile uint32_t *)SRAM_MEM_CONFIG;
    if ((cfg & 0x1) != 0x1) {
        print2("[FAILED] edac_en bit not set: read=0x%x\n", cfg);
        return;
    }

    /* 注入单错并读取触发单错中断 */
    MyBsp_CreateSramDataSEU((uint32_t)addr);
    MyBsp_CreateSramEdacSEU((uint32_t)addr);
    dummy = *addr;
    print2("[INFO] Single-bit error injected at 0x%08x, check ISR\n", (uint32_t)addr);
    MyBsp_SramSEUClear();

    /* 注入多错并读取触发多错/双错中断 */
    MyBsp_CreateSramDataTBU((uint32_t)addr);
    MyBsp_CreateSramEdacTBU((uint32_t)addr);
    dummy = *addr;
    print2("[INFO] Multi-bit error injected at 0x%08x, check ISR\n", (uint32_t)addr);
    MyBsp_SramTBEClear();

    /* 将 edac_en 置 0 并验证 */
    *(volatile uint32_t *)SRAM_MEM_CONFIG = cfg & ~0x1;
    if ((*(volatile uint32_t *)SRAM_MEM_CONFIG & 0x1) != 0) {
        print2("[FAILED] Failed to clear edac_en bit\n");
        return;
    }
    print2("[INFO] edac_en disabled successfully\n");

    /* EDAC 禁用后重复单/多错注入，应无中断 */
    MyBsp_CreateSramDataSEU((uint32_t)addr);
    MyBsp_CreateSramEdacSEU((uint32_t)addr);
    dummy = *addr;
    MyBsp_CreateSramDataTBU((uint32_t)addr);
    MyBsp_CreateSramEdacTBU((uint32_t)addr);
    dummy = *addr;
    print2("[INFO] Errors injected with EDAC disabled, no ISR should occur\n");

    /* 将 edac_en 置 1 并验证 */
    *(volatile uint32_t *)SRAM_MEM_CONFIG |= 0x1;
    if ((*(volatile uint32_t *)SRAM_MEM_CONFIG & 0x1) != 1) {
        print2("[FAILED] Failed to set edac_en bit\n");
        return;
    }
    print2("[INFO] edac_en re-enabled successfully\n");

    /* 在另一地址重复步骤 2~5 */
    volatile uint32_t *addr2 = (uint32_t *)(SRAM_START_ADDR + 0x1000);
    MyBsp_CreateSramDataSEU((uint32_t)addr2);
    MyBsp_CreateSramEdacSEU((uint32_t)addr2);
    dummy = *addr2;
    print2("[INFO] Single-bit error injected at 0x%08x, check ISR\n", (uint32_t)addr2);
    MyBsp_SramSEUClear();

    MyBsp_CreateSramDataTBU((uint32_t)addr2);
    MyBsp_CreateSramEdacTBU((uint32_t)addr2);
    dummy = *addr2;
    print2("[INFO] Multi-bit error injected at 0x%08x, check Data Abort\n", (uint32_t)addr2);
    MyBsp_SramTBEClear();

    print2("[PASSED] uc_st_1_3: SRAM EDAC error injection test completed\n");
}

// TODO
void uc_st_1_4(void) {
    print2("[INFO] uc_st_1_4: Starting SRAM EDAC bypass/injection test\n");

    uint64_t writeData = 0x0123456789ABCDEFULL;
    uint32_t addrA = SRAM_START_ADDR;  /* 8Byte 对齐 */
    uint32_t addrB = addrA + 32;
    uint64_t expectedEcc, data64;
    uint32_t injectedEcc, actualLow, actualHigh, edac_config, data32;

    /* 1. 向地址 A 写入 8 Byte 内容 */
    *((volatile uint64_t *)addrA) = writeData;

    /* 2. 设置读旁路到 A 并触发 */
    *((volatile uint32_t *)SRAM_EDAC_READ_BYPASS_ADDR) = addrA;
    *((volatile uint32_t *)SRAM_EDAC_READ_BYPASS_ADDR);
    edac_config = *((volatile uint32_t *)SRAM_MEM_CONFIG);
    *((volatile uint32_t *)SRAM_MEM_CONFIG) = edac_config | (1u << 1); /* 使能读旁路 */
    data32 = *((volatile uint32_t *)addrA); /* 触发读旁路 */

    /* 3. 读取读旁路低位/高位寄存器，判断校验码 */
    actualLow  = *((volatile uint32_t *)SRAM_EDAC_READBYPASS_DATA_LOW);
    actualHigh = *((volatile uint32_t *)SRAM_EDAC_READBYPASS_DATA_HIGH);
    expectedEcc = gen_sram_edac(writeData);
    if ((uint32_t)expectedEcc != actualLow || (uint32_t)(expectedEcc >> 32) != actualHigh) {
        print2(
            "[FAILED] Read bypass ECC mismatch at 0x%x: readLow=0x%x, readHigh=0x%x, expLow=0x%x, expHigh=0x%x\n",
            addrA, actualLow, actualHigh,
            (uint32_t)expectedEcc, (uint32_t)(expectedEcc >> 32)
        );
        return;
    }

    /* 4. 设置写旁路到 A、注入校验码，然后触发写旁路 */
    injectedEcc = 0x1F1F1F1F;
    *((volatile uint32_t *)SRAM_EDAC_WRITE_BYPASS_ADDR) = addrA;
    *((volatile uint32_t *)SRAM_EDAC_BYPASS_DATAIN)     = (uint32_t)injectedEcc;
    edac_config = *((volatile uint32_t *)SRAM_MEM_CONFIG);
    *((volatile uint32_t *)SRAM_MEM_CONFIG) = edac_config | (1u << 2); /* 使能写旁路 */
    *((volatile uint32_t *)addrA) = 0xFFFFFFFF; /* 触发写旁路 */

    /* 5. 在地址 A 再次触发读旁路 */
    *((volatile uint32_t *)SRAM_EDAC_READ_BYPASS_ADDR) = addrA;
    data64 = *((volatile uint64_t *)addrA); /* 触发读旁路 */

    /* 6. 验证注入的校验码 */
    actualLow  = *((volatile uint32_t *)SRAM_EDAC_READBYPASS_DATA_LOW);
    actualHigh = *((volatile uint32_t *)SRAM_EDAC_READBYPASS_DATA_HIGH);
    if ((uint32_t)injectedEcc != actualLow || (uint32_t)(expectedEcc >> 32) != actualHigh) {
        print2(
            "[FAILED] Injected ECC mismatch at 0x%x: readLow=0x%x, readHigh=0x%x, expLow=0x%x, expHigh=0x%x\n",
            addrA, actualLow, actualHigh,
            (uint32_t)injectedEcc, (uint32_t)(expectedEcc >> 32)
        );
        return;
    }

    /* 7. 修改写旁路到地址 B */
    *((volatile uint32_t *)SRAM_EDAC_WRITE_BYPASS_ADDR) = addrB;

    /* 8. 向整个 SRAM 空间写入全 1 */
    for (uint32_t off = 0; off <= (SRAM_END_ADDR - SRAM_START_ADDR); off += 8) {
        *((volatile uint64_t *)(SRAM_START_ADDR + off)) = ~0ULL;
    }

    /* 9. 读旁路到起始地址，触发并验证校验码与全 1 数据一致 */
    *((volatile uint32_t *)SRAM_EDAC_READ_BYPASS_ADDR) = SRAM_START_ADDR;
    data64 = *((volatile uint64_t *)SRAM_START_ADDR); /* 触发读旁路 */
    actualLow  = *((volatile uint32_t *)SRAM_EDAC_READBYPASS_DATA_LOW);
    actualHigh = *((volatile uint32_t *)SRAM_EDAC_READBYPASS_DATA_HIGH);
    expectedEcc = gen_sram_edac(~0ULL);
    if ((uint32_t)expectedEcc != actualLow || (uint32_t)(expectedEcc >> 32) != actualHigh) {
        print2(
            "[FAILED] ECC mismatch at start 0x%x: readLow=0x%x, readHigh=0x%x, expLow=0x%x, expHigh=0x%x\n",
            SRAM_START_ADDR, actualLow, actualHigh,
            (uint32_t)expectedEcc, (uint32_t)(expectedEcc >> 32)
        );
        return;
    }

    /* 10~11. 遍历所有 8Byte 对齐地址，验证校验码 */
    for (uint32_t off = 8; off <= (SRAM_END_ADDR - SRAM_START_ADDR); off += 8) {
        uint32_t cur = SRAM_START_ADDR + off;
        print2("cur is: 0x%x\n", cur);
        *((volatile uint32_t *)SRAM_EDAC_READ_BYPASS_ADDR) = cur;
        data64 = *((volatile uint64_t *)cur); /* 触发读旁路 */
        actualLow  = *((volatile uint32_t *)SRAM_EDAC_READBYPASS_DATA_LOW);
        actualHigh = *((volatile uint32_t *)SRAM_EDAC_READBYPASS_DATA_HIGH);

        if (cur == addrB) {
            expectedEcc = injectedEcc | (gen_sram_edac(~0ULL) & ~0xFFFFFFFFULL);
        } else {
            expectedEcc = gen_sram_edac(~0ULL);
        }

        if ((uint32_t)expectedEcc != actualLow || (uint32_t)(expectedEcc >> 32) != actualHigh) {
            print2(
                "[FAILED] ECC mismatch at 0x%x: readLow=0x%x, readHigh=0x%x, expLow=0x%x, expHigh=0x%x\n",
                cur, actualLow, actualHigh,
                (uint32_t)expectedEcc, (uint32_t)(expectedEcc >> 32)
            );
            return;
        }
    }

    print2("[PASSED] uc_st_1_4: SRAM EDAC bypass/injection test completed successfully\n");
}
