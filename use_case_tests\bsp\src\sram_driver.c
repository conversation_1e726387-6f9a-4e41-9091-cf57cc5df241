#include "sram.h"
#include "gic.h"
#include "types.h"

extern int irq_count;

/* SRAM ISR */
void sram_obe_isr() {
    irq_count++;
    print2("[IRQ] Sram single bit error\n");

    /* 清除中断 */
    MyBsp_SramSEUClear();
}

// void sram_tbe_isr() {
//     irq_count++;
//     while(1) {

//     }
// }

static inline uint8_t gen_8_5(uint8_t data)
{
    /* 数据位提取 */
	uint8_t d0 = (data >> 0) & 1;
	uint8_t d1 = (data >> 1) & 1;
	uint8_t d2 = (data >> 2) & 1;
	uint8_t d3 = (data >> 3) & 1;
	uint8_t d4 = (data >> 4) & 1;
	uint8_t d5 = (data >> 5) & 1;
	uint8_t d6 = (data >> 6) & 1;
	uint8_t d7 = (data >> 7) & 1;

	/* EDAC 校验位生成 */
	uint8_t m0 = d0 ^ d2 ^ d3 ^ d5 ^ d6 ^ d7;
	uint8_t m1 = d0 ^ d1 ^ d2 ^ d3;
	uint8_t m2 = d0 ^ d4 ^ d5 ^ d6;
	uint8_t m3 = d1 ^ d2 ^ d4 ^ d5 ^ d7;
	uint8_t m4 = d1 ^ d3 ^ d4 ^ d6 ^ d7;

    return m0 | (m1 << 1) | (m2 << 2) | (m3 << 3) | (m4 << 4);
}

static uint32_t gen_sram_edac(uint32_t word) {
    /* 为 4 字节数据生成 20 bit EDAC */
    uint8_t edac[4];
    for (int i = 0; i < 4; i++) {
        edac[i] = gen_8_5((word >> (i * 8)) & 0xFF);
    }
    return *(uint32_t *)edac;
}

void MyBsp_CreateSramDataSEU(U32 addr) {
    /* 开启读旁路 */
    *(volatile uint32_t *)SRAM_MEM_CONFIG |= 0x2;

    /* 读出原始数据及校验码 */
    uint32_t data = *(volatile uint32_t *)addr;
    
    /* 生成正确的 32 bit 校验码 */
    uint32_t edac = gen_sram_edac(data);

    /* 关闭读旁路 */
    *(volatile uint32_t *)SRAM_MEM_CONFIG &= ~0x2;

    /* 开启 EDAC 写旁路 */
    *(volatile uint32_t *)SRAM_MEM_CONFIG |= 0x4;

    /* 设置写旁路地址 */
    *(volatile uint32_t *)SRAM_EDAC_WRITE_BYPASS_ADDR = addr;

    /* 设置注入数据 */
    *(volatile uint32_t *)SRAM_EDAC_BYPASS_DATAIN = edac;

    /* 数据位造单错 */
    data ^= 0x1;

    /* 触发写旁路 */
    *(volatile uint32_t *)addr = data; /* 触发写旁路 */

    /* 关闭 EDAC 写旁路 */
    *(volatile uint32_t *)SRAM_MEM_CONFIG &= ~0x4;
}

void MyBsp_CreateSramDataTBU(U32 addr) {
    /* 开启读旁路 */
    *(volatile uint32_t *)SRAM_MEM_CONFIG |= 0x2;

    /* 读出原始数据及校验码 */
    uint32_t data = *(volatile uint32_t *)addr;
    
    /* 生成正确的 32 bit 校验码 */
    uint32_t edac = gen_sram_edac(data);

    /* 关闭读旁路 */
    *(volatile uint32_t *)SRAM_MEM_CONFIG &= ~0x2;

    /* 开启 EDAC 写旁路 */
    *(volatile uint32_t *)SRAM_MEM_CONFIG |= 0x4;

    /* 设置写旁路地址 */
    *(volatile uint32_t *)SRAM_EDAC_WRITE_BYPASS_ADDR = addr;

    /* 设置注入数据 */
    *(volatile uint32_t *)SRAM_EDAC_BYPASS_DATAIN = edac;

    /* 数据位造双错 */
    data ^= 0x3;

    /* 触发写旁路 */
    *(volatile uint32_t *)addr = data; /* 触发写旁路 */

    /* 关闭 EDAC 写旁路 */
    *(volatile uint32_t *)SRAM_MEM_CONFIG &= ~0x4;
}

void MyBsp_CreateSramEdacSEU(U32 addr) {
    /* 开启读旁路 */
    *(volatile uint32_t *)SRAM_MEM_CONFIG |= 0x2;

    /* 读出原始数据及校验码 */
    uint32_t data = *(volatile uint32_t *)addr;
    
    /* 生成正确的 32 bit 校验码 */
    uint32_t edac = gen_sram_edac(data);

    /* 关闭读旁路 */
    *(volatile uint32_t *)SRAM_MEM_CONFIG &= ~0x2;

    /* 开启 EDAC 写旁路 */
    *(volatile uint32_t *)SRAM_MEM_CONFIG |= 0x4;

    /* 设置写旁路地址 */
    *(volatile uint32_t *)SRAM_EDAC_WRITE_BYPASS_ADDR = addr;

    /* 校验码造单错 */
    edac ^= 0x1;

    /* 设置注入数据 */
    *(volatile uint32_t *)SRAM_EDAC_BYPASS_DATAIN = edac;

    /* 触发写旁路 */
    *(volatile uint32_t *)addr = data; /* 触发写旁路 */

    /* 关闭 EDAC 写旁路 */
    *(volatile uint32_t *)SRAM_MEM_CONFIG &= ~0x4;
}

void MyBsp_CreateSramEdacTBU(U32 addr) {
    /* 开启读旁路 */
    *(volatile uint32_t *)SRAM_MEM_CONFIG |= 0x2;

    /* 读出原始数据及校验码 */
    uint32_t data = *(volatile uint32_t *)addr;
    
    /* 生成正确的 32 bit 校验码 */
    uint32_t edac = gen_sram_edac(data);

    /* 关闭读旁路 */
    *(volatile uint32_t *)SRAM_MEM_CONFIG &= ~0x2;

    /* 开启 EDAC 写旁路 */
    *(volatile uint32_t *)SRAM_MEM_CONFIG |= 0x4;

    /* 设置写旁路地址 */
    *(volatile uint32_t *)SRAM_EDAC_WRITE_BYPASS_ADDR = addr;

    /* 校验码造双错 */
    edac ^= 0x3;

    /* 设置注入数据 */
    *(volatile uint32_t *)SRAM_EDAC_BYPASS_DATAIN = edac;

    /* 触发写旁路 */
    *(volatile uint32_t *)addr = data; /* 触发写旁路 */

    /* 关闭 EDAC 写旁路 */
    *(volatile uint32_t *)SRAM_MEM_CONFIG &= ~0x4;
}

void MyBsp_SramSEUClear() {
    /* 清除单错中断 */
    *(volatile uint32_t *)SRAM_ONE_BIT_CLEAR = 0x1;
}

void MyBsp_SramTBEClear() {
    /* 清除多错中断 */
    *(volatile uint32_t *)SRAM_TWO_BIT_CLEAR = 0x1;
}
