/*
 * 片上 SRAM 及控制寄存器模块测试用例实现接口
 *
 * 测试用例范围：UC-ST-1-1 ~ UC-ST-1-11
 */

#ifndef UC_ST_1_H
#define UC_ST_1_H

void uc_st_1_1(void);
void uc_st_1_2(void); /* 开启 QEMU -d guest_errors 并配合 QEMU LOG 测试 */
void uc_st_1_3(void);
void uc_st_1_4(void);
void uc_st_1_5(void);
void uc_st_1_6(void);
void uc_st_1_7(void);
void uc_st_1_8(void);
void uc_st_1_9(void);
void uc_st_1_10(void);
void uc_st_1_11(void);

#endif /* UC_ST_1_H */
