#include "gic.h"

/* 函数指针定义（内部用）*/
typedef int (*ISRHandler)();

/* 全局 IRQ 计数变量 - 目前用于测试 */
int irq_count = 0;

/* 全局中断映射表 - 各片上外设的 ISR 应注册在此处 */
static ISR_MAP isr_map[TOTAL_INT_NUM] = {
    /* SP804 ISR 注册 */
    [TIMER_IRQ_ID] = {
        .Type    = ISR_TYPE_EXTERNAL,           /* 类型设置为外部中断 */
        .Handler = (uintptr_t)sp804_isr,        /* 地址需按实际函数名设置 */
        .Arguments = {0, 0}                     /* 参数按需配置 */
    },
    /* UART0 ISR 注册 */
    [UART0_IRQ_ID] = {
        .Type    = ISR_TYPE_EXTERNAL,           /* 类型设置为外部中断 */
        .Handler = (uintptr_t)uart0_isr,        /* 地址需按实际函数名设置 */
        .Arguments = {0, 0}                     /* 参数按需配置 */
    },
    /*16路GPIO ISR 注册*/
    /* 第1路注册 */
    [GPIO0_IRQ_ID] = {  
        .Type    = ISR_TYPE_EXTERNAL,           /* 类型设置为外部中断 */
        .Handler = (uintptr_t)gpio_isr,        /* 地址需按实际函数名设置 */
        .Arguments = {0, 0}                     /* 参数按需配置 */
     },
    /* 第2路注册 */
    [GPIO1_IRQ_ID] = {
       .Type    = ISR_TYPE_EXTERNAL,           /* 类型设置为外部中断 */
       .Handler = (uintptr_t)gpio_isr,        /* 地址需按实际函数名设置 */
       .Arguments = {0, 0}                     /* 参数按需配置 */ 
    },
    /* EMIF 单错ISR 注册 */
    [EMIF_IRQ_SE_ID] = {
        .Type    = ISR_TYPE_EXTERNAL,               // 类型设置为外部中断
        .Handler = (uintptr_t)emif_se_isr,            // 地址需按实际函数名设置
        .Arguments = {0, 0}                         // 参数按需配置
    },
    /* EMIF 多错ISR 注册 */
    [EMIF_IRQ_DE_ID] = {
        .Type    = ISR_TYPE_EXTERNAL,               // 类型设置为外部中断
        .Handler = (uintptr_t)emif_de_isr,            // 地址需按实际函数名设置
        .Arguments = {0, 0}                         // 参数按需配置
    },
    /* EMIF IO ISR 注册 */
    [EMIF_IRQ_IO_ID] = {
        .Type    = ISR_TYPE_EXTERNAL,               // 类型设置为外部中断
        .Handler = (uintptr_t)emif_io_isr,            // 地址需按实际函数名设置
        .Arguments = {0, 0}                         // 参数按需配置
    },
    [SRAM_OBE_IRQ_ID] = {
        .Type    = ISR_TYPE_EXTERNAL,           /* 类型设置为外部中断 */
        .Handler = (uintptr_t)sram_obe_isr,     /* 地址需按实际函数名设置 */
        .Arguments = {0, 0}                     /* 参数按需配置 */
    },
    [SRAM_TBE_IRQ_ID] = {
        .Type    = ISR_TYPE_EXTERNAL,           /* 类型设置为外部中断 */
        .Handler = (uintptr_t)sram_tbe_isr,     /* 地址需按实际函数名设置 */
        .Arguments = {0, 0}                     /* 参数按需配置 */
    },
    [SDRAM_OBE_IRQ_ID] = {
        .Type    = ISR_TYPE_EXTERNAL,           /* 类型设置为外部中断 */
        .Handler = (uintptr_t)sdram_obe_isr,     /* 地址需按实际函数名设置 */
        .Arguments = {0, 0}                     /* 参数按需配置 */
    },
    [SDRAM_TBE_IRQ_ID] = {
        .Type    = ISR_TYPE_EXTERNAL,           /* 类型设置为外部中断 */
        .Handler = (uintptr_t)sdram_tbe_isr,     /* 地址需按实际函数名设置 */
        .Arguments = {0, 0}                     /* 参数按需配置 */
    },
    [A9MPCORE_GLOBAL_TIMER_IRQ_ID] = {
        .Type    = ISR_TYPE_INTERNAL,           /* 类型设置为内部中断 */
        .Handler = (uintptr_t)global_timer_isr, /* 地址需按实际函数名设置 */
        .Arguments = {0, 0}                     /* 参数按需配置 */
    }
};

/*
 * GIC 初始化
 */
void gic_init() {
    WRITE_32(GICD_ICCDCR, 0b11);                     /* 使能 GIC 分配器 */
    WRITE_32(GICD_ICDISERn, 0xFFFFFFFF);             /* 使能中断 ID 0 ~ 31 */
    WRITE_32((GICD_ICDISERn + 0x04), 0xFFFFFFFF);    /* 使能中断 ID 32 ~ 63 */
    WRITE_32((GICD_ICDISERn + 0x08), 0xFFFFFFFF);    /* 使能中断 ID 64 ~ 95 */
    WRITE_32(GICC_ICCPMR, 0xFF);                     /* 设置优先级阈值为 level 255 (最低) */
    WRITE_32(GICC_ICCICR, 0b10111);                  /* 使能 CPU 接口，配置中断为 FIQ 模式 */

    /* 开启 IRQ 中断 */
    __asm__ volatile ("mrs r0, cpsr");
    __asm__ volatile ("bic r0, r0, #0xc0");
    __asm__ volatile ("msr cpsr_c, r0");
}

/*
 * IRQ 异常服务例程
 */
void gic_handle_irq(void)
{
    /* 获取中断 ID */
    uint32_t interrupt_id = READ_32(GICC_ICCIAR) & 0x3FF;

    /* 处理特殊中断号 */
    if (interrupt_id == 1023) {
        WRITE_32(GICC_ICCEOIR, interrupt_id);
        return;
    }

    /* 记录中断屏蔽级别 */
    uint32_t old_int_mask_level = READ_32(GICC_ICCPMR);

    /* 屏蔽低优先级中断 */
    WRITE_32(GICC_ICCPMR, READ_32(GICC_ICCRPR) & 0xFF);

    /* 开中断 */
    __asm__ volatile ("cpsie i");
    __asm__ volatile ("dsb");
	__asm__ volatile ("isb");
    
    /* 处理特殊中断号 */
    if (interrupt_id == 1023) {
        return; /* 伪中断，直接返回 */
    }
    
    /* ISR 查找 */
    for (int i = 0; i < TOTAL_INT_NUM; i++) {
        if (i == interrupt_id) {
            /* ISR 调用 */
            ISRHandler handler = (ISRHandler)isr_map[i].Handler;
            if (handler) {
                handler(isr_map[i].Arguments[0], isr_map[i].Arguments[1]);
            }
            break;
        }
    }

    /* 关中断 */
    __asm__ volatile ("cpsid i");
    __asm__ volatile ("dsb");
	__asm__ volatile ("isb");
    
    /* 结束中断 */
    WRITE_32(GICC_ICCEOIR, interrupt_id);

    /* 恢复原中断优先级 */
    WRITE_32(GICC_ICCPMR, old_int_mask_level);
}
